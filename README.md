# MetronomeApp 🎵

A professional metronome app for iOS featuring advanced timing accuracy, multiple sound types, and modern design.

## Features

- 🎯 **Sample-Accurate Timing**: Professional-grade timing using AVAudioEngine scheduling
- 🎨 **Modern UI**: Sleek dark design with turquoise accents and smooth animations
- 🎛️ **Advanced Controls**: 
  - Tap tempo functionality
  - Linear BPM slider with quick adjustments
  - Volume control
  - Multiple rhythm patterns
- 🔊 **Multiple Sound Types**: Wood Block, Click, Tick, and Hi-Hat sounds
- ⚡ **Smart Features**: 
  - Visual count-in (4 beats)
  - Settings persistence
  - Beat indicators with animations
- 📱 **iOS Native**: Built with SwiftUI and modern iOS patterns

## Technical Improvements

- **Enhanced Audio Engine**: AVAudioEngine with sample-accurate scheduling
- **Better Memory Management**: Pre-generated audio buffers with recycling
- **Settings Persistence**: UserDefaults integration for all preferences
- **Professional Features**: Tap tempo, count-in, volume control
- **Performance Optimized**: Reduced CPU usage and improved battery life

## Project Structure

```
MetronomeApp/
├── MetronomeApp/
│   ├── MetronomeApp.swift              # App entry point with audio session
│   ├── ContentView.swift               # Main interface with controls
│   ├── MetronomeEngine.swift           # Advanced audio engine
│   ├── SettingsView.swift              # Settings screen
│   ├── LinearSlider.swift              # Custom BPM slider
│   ├── ColorExtension.swift            # Color utilities
│   ├── Assets.xcassets/                # App icons and assets
│   └── Info.plist                      # App configuration
├── MetronomeApp.xcodeproj/             # Xcode project
└── README.md                           # This file
```

## Key Features

### Professional Audio Engine
- **Sample-accurate timing** eliminates drift at any BPM
- **Multiple sound types** for different musical preferences
- **Volume control** with real-time adjustment
- **11 rhythm patterns** including triplets, swing, and syncopation

### Smart User Interface
- **Tap tempo** for natural BPM detection
- **Visual count-in** with 4-beat countdown
- **Beat indicators** with smooth animations
- **Settings persistence** across app launches

### Enhanced Controls
- **Quick BPM adjustments** (-10, -1, +1, +10 buttons)
- **Beats per bar** cycling (2-8 beats)
- **Note value** selection (2, 4, 8, 16)
- **Rhythm pattern** selection with descriptions

## How to Run

1. Open `MetronomeApp.xcodeproj` in Xcode
2. Select your target device or iOS Simulator
3. Build and run the project (⌘+R)

## Requirements

- Xcode 15.0+
- iOS 16.0+
- Swift 5.9+

## Development Notes

This version represents a complete overhaul of the metronome engine featuring:
- **Professional-grade timing accuracy** using AVAudioEngine
- **Multiple sound synthesis** with custom audio generation
- **Modern SwiftUI architecture** with proper state management
- **Enhanced user experience** with tap tempo and visual feedback
- **Performance optimizations** for battery life and responsiveness

## Audio Features

### Sound Types
- **Wood Block**: Enhanced wooden metronome sound with harmonic richness
- **Click**: High-pitched traditional click sound
- **Tick**: Classic metronome tick with balanced frequency
- **Hi-Hat**: Drum-style hi-hat with noise synthesis

### Rhythm Patterns
- Straight, Eighth Notes, Sixteenth Notes
- Triplet, Sextuplet variations
- Swing, Shuffle, Dotted rhythms
- Syncopated patterns for advanced practice

## License

This project is for educational and personal use.

---

*Crafted with 🎨 using SwiftUI and professional audio engineering practices*