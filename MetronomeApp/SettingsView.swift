import SwiftUI

struct SettingsView: View {
    @ObservedObject var metronome: MetronomeEngine
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                Color(hex: "1a1a2e")
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 30) {
                        // Volume Control Section
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Volume")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            VStack(spacing: 10) {
                                HStack {
                                    Image(systemName: "speaker.fill")
                                        .foregroundColor(.white.opacity(0.6))
                                    Slider(value: $metronome.volume, in: 0...1)
                                        .tint(Color(hex: "4ecdc4"))
                                    Image(systemName: "speaker.wave.3.fill")
                                        .foregroundColor(.white.opacity(0.6))
                                }
                                
                                Text(String(format: "%.0f%%", metronome.volume * 100))
                                    .font(.system(size: 14))
                                    .foregroundColor(.white.opacity(0.7))
                            }
                            .padding()
                            .background(Color.white.opacity(0.1))
                            .cornerRadius(10)
                        }
                        
                        VStack(alignment: .leading, spacing: 20) {
                            Text("Sound Type")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            ForEach(MetronomeEngine.SoundType.allCases, id: \.self) { soundType in
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(soundType.rawValue)
                                            .foregroundColor(.white.opacity(0.8))
                                            .font(.system(size: 16, weight: .medium))
                                        
                                        Text(soundType.description)
                                            .foregroundColor(.white.opacity(0.6))
                                            .font(.system(size: 12))
                                    }
                                    
                                    Spacer()
                                    
                                    if metronome.soundType == soundType {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(Color(hex: "4ecdc4"))
                                    }
                                }
                                .padding()
                                .background(metronome.soundType == soundType ? 
                                          Color(hex: "4ecdc4").opacity(0.2) : 
                                          Color.white.opacity(0.1))
                                .cornerRadius(10)
                                .onTapGesture {
                                    metronome.soundType = soundType
                                }
                            }
                        }
                        
                        
                        VStack(alignment: .leading, spacing: 20) {
                            Text("Rhythm Patterns")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                                ForEach(MetronomeEngine.RhythmPattern.allCases, id: \.self) { pattern in
                                    RhythmPatternButton(
                                        pattern: pattern,
                                        isSelected: metronome.rhythmPattern == pattern,
                                        action: { metronome.rhythmPattern = pattern }
                                    )
                                }
                            }
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(Color(hex: "4ecdc4"))
                }
            }
        }
    }
}

struct RhythmPatternButton: View {
    let pattern: MetronomeEngine.RhythmPattern
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(pattern.rawValue)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.9))
                    .multilineTextAlignment(.center)
                
                Text(pattern.description)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .white.opacity(0.6))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .padding(.horizontal, 16)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color(hex: "4ecdc4") : Color.white.opacity(0.08))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isSelected ? Color(hex: "4ecdc4") : Color.white.opacity(0.2), 
                        lineWidth: isSelected ? 2 : 1
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}