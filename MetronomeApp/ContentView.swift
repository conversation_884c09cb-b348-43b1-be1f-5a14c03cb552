import SwiftUI

struct ContentView: View {
    @StateObject private var metronome = MetronomeEngine()
    @State private var showSettings = false
    @State private var playButtonPressed = false
    @State private var beatPulse = false
    @State private var activeBeatIndex = 0
    @State private var beatIndicatorGlows = Array(repeating: false, count: 8) // 支持最多8拍
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                LinearGradient(
                    colors: [Color(hex: "1a1a2e"), Color(hex: "0f0f1e")],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    Spacer()
                    
                    // Linear Slider with integrated BPM display
                    VStack(spacing: 25) {
                        LinearSlider(value: $metronome.bpm, range: 30...250)
                        
                        // Volume Control
                        VStack(spacing: 10) {
                            HStack {
                                Image(systemName: "speaker.fill")
                                    .foregroundColor(.white.opacity(0.6))
                                Slider(value: $metronome.volume, in: 0...1)
                                    .tint(Color(hex: "4ecdc4"))
                                Image(systemName: "speaker.wave.3.fill")
                                    .foregroundColor(.white.opacity(0.6))
                            }
                            .padding(.horizontal, 20)
                        }
                        
                        // Beat Indicators - 居中对齐版本
                        HStack(spacing: 15) {
                            // 左侧占位空间
                            Spacer()
                            
                            // 只显示实际需要的beat indicators
                            ForEach(1...metronome.beatsPerBar, id: \.self) { beat in
                                let isActive = metronome.currentBeat == beat && metronome.isPlaying
                                let isMainBeat = beat == 1
                                let beatIndex = beat - 1
                                let isGlowing = beatIndex < beatIndicatorGlows.count ? beatIndicatorGlows[beatIndex] : false
                                
                                ZStack {
                                    // 背景圆环
                                    Circle()
                                        .stroke(Color.white.opacity(0.1), lineWidth: 2)
                                        .frame(width: 16, height: 16)
                                    
                                    // 主要指示器
                                    Circle()
                                        .fill(
                                            isActive ? 
                                                (isMainBeat ? 
                                                    LinearGradient(
                                                        colors: [Color(hex: "ff6b6b"), Color(hex: "ff8e8e")],
                                                        startPoint: .topLeading,
                                                        endPoint: .bottomTrailing
                                                    ) :
                                                    LinearGradient(
                                                        colors: [Color(hex: "4ecdc4"), Color(hex: "6de5d8")],
                                                        startPoint: .topLeading,
                                                        endPoint: .bottomTrailing
                                                    )
                                                ) :
                                                LinearGradient(
                                                    colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                )
                                        )
                                        .frame(width: isActive ? 12 : 8, height: isActive ? 12 : 8)
                                        .scaleEffect(isActive ? (isGlowing ? 1.4 : 1.2) : 1.0)
                                        .shadow(
                                            color: isActive ? 
                                                (isMainBeat ? Color(hex: "ff6b6b") : Color(hex: "4ecdc4")) : 
                                                Color.clear,
                                            radius: isGlowing ? 8 : 4
                                        )
                                        .animation(.easeOut(duration: 0.08), value: isActive)
                                        .animation(.easeOut(duration: 0.08), value: isGlowing)
                                    
                                    // 节拍编号（小字体）
                                    if !isActive {
                                        Text("\(beat)")
                                            .font(.system(size: 6, weight: .medium))
                                            .foregroundColor(.white.opacity(0.4))
                                    }
                                }
                                .transition(.scale.combined(with: .opacity))
                            }
                            
                            // 右侧占位空间
                            Spacer()
                        }
                        .padding(.vertical, 10)
                        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: metronome.beatsPerBar)
                    }
                    .padding(.bottom, 20)
                    
                    // Control Buttons
                    HStack(spacing: 40) {
                        // Tap Tempo Button
                        Button(action: {
                            UIImpactFeedbackGenerator(style: .light).impactOccurred()
                            metronome.tapTempo()
                        }) {
                            VStack(spacing: 5) {
                                Image(systemName: "hand.tap.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(Color(hex: "4ecdc4"))
                                Text("TAP")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                        .disabled(metronome.isPlaying)
                        .opacity(metronome.isPlaying ? 0.5 : 1.0)
                        
                        // Play/Pause Button
                        Button(action: {
                            // 触觉反馈
                            UIImpactFeedbackGenerator(style: .heavy).impactOccurred()
                            
                            // 即时视觉反馈
                            withAnimation(.easeOut(duration: 0.1)) {
                                playButtonPressed = true
                            }
                            
                            // 延迟恢复按钮状态，营造按下效果
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                withAnimation(.easeOut(duration: 0.1)) {
                                    playButtonPressed = false
                                }
                            }
                            
                            // 执行播放/暂停逻辑
                            if metronome.isPlaying {
                                metronome.stop()
                            } else {
                                metronome.start()
                            }
                        }) {
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: metronome.isPlaying ? 
                                                [Color(hex: "ff6b6b"), Color(hex: "ee5a6f")] :
                                                [Color(hex: "4ecdc4"), Color(hex: "44a3aa")],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 100, height: 100)
                                    .scaleEffect(playButtonPressed ? 0.95 : 1.0)
                                    .animation(.easeOut(duration: 0.1), value: playButtonPressed)
                                
                                Image(systemName: metronome.isPlaying ? "pause.fill" : "play.fill")
                                    .font(.system(size: 40))
                                    .foregroundColor(.white)
                                    .offset(x: metronome.isPlaying ? 0 : 3)
                                    .scaleEffect(playButtonPressed ? 0.9 : 1.0)
                                    .animation(.easeOut(duration: 0.1), value: playButtonPressed)
                            }
                        }
                        .shadow(color: metronome.isPlaying ? 
                                Color(hex: "ff6b6b").opacity(0.5) : 
                                Color(hex: "4ecdc4").opacity(0.5), 
                                radius: 20)
                        
                        // Reset Tap Button
                        Button(action: {
                            UIImpactFeedbackGenerator(style: .light).impactOccurred()
                            metronome.resetTapTempo()
                        }) {
                            VStack(spacing: 5) {
                                Image(systemName: "arrow.clockwise")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white.opacity(0.6))
                                Text("RESET")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                        .disabled(metronome.isPlaying)
                        .opacity(metronome.isPlaying ? 0.5 : 1.0)
                    }
                    .padding(.vertical, 40)
                    
                    Spacer()
                    
                    // Bottom Controls
                    HStack(spacing: 0) {
                        // Beats Control
                        VStack(spacing: 5) {
                            Text("\(metronome.beatsPerBar)")
                                .font(.system(size: 28, weight: .light))
                                .foregroundColor(.white)
                            Text("BEATS")
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(.white.opacity(0.5))
                        }
                        .frame(maxWidth: .infinity)
                        .onTapGesture {
                            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                            metronome.cycleBeatsPerBar()
                        }
                        
                        // Note Control
                        VStack(spacing: 5) {
                            Text("\(metronome.noteValue)")
                                .font(.system(size: 28, weight: .light))
                                .foregroundColor(.white)
                            Text("NOTE")
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(.white.opacity(0.5))
                        }
                        .frame(maxWidth: .infinity)
                        .onTapGesture {
                            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                            metronome.cycleNoteValue()
                        }
                        
                        // Sound Type Display
                        VStack(spacing: 5) {
                            Text(metronome.soundType.description.prefix(4).uppercased())
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                            Text("SOUND")
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(.white.opacity(0.5))
                        }
                        .frame(maxWidth: .infinity)
                        
                        // Settings Button
                        Button(action: { showSettings = true }) {
                            Image(systemName: "gearshape.fill")
                                .font(.system(size: 26))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, geometry.safeAreaInsets.bottom + 30)
                }
            }
        }
        .onReceive(metronome.beatPublisher) { beat in
            // 更新活跃的节拍指示器
            activeBeatIndex = beat - 1
            
            // Beat indicator发光效果
            if activeBeatIndex < beatIndicatorGlows.count {
                withAnimation(.easeOut(duration: 0.05)) {
                    beatIndicatorGlows[activeBeatIndex] = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.easeOut(duration: 0.1)) {
                        if activeBeatIndex < beatIndicatorGlows.count {
                            beatIndicatorGlows[activeBeatIndex] = false
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            SettingsView(metronome: metronome)
        }
    }
}