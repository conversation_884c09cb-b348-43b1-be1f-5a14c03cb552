import SwiftUI

struct LinearSlider: View {
    @Binding var value: Int
    let range: ClosedRange<Int>
    
    @State private var isDragging = false
    @State private var lastHapticValue: Int = 0
    
    private let trackHeight: CGFloat = 6
    private let knobSize: CGFloat = 28
    
    var body: some View {
        VStack(spacing: 30) {
            // BPM Display
            VStack(spacing: 4) {
                Text("\(value)")
                    .font(.system(size: 72, weight: .light, design: .rounded))
                    .foregroundColor(.white)
                    .scaleEffect(isDragging ? 1.05 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isDragging)
                
                Text("BPM")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
            .frame(width: 200, height: 200)
            .background(
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color(hex: "4ecdc4").opacity(0.3),
                                Color(hex: "44a3aa").opacity(0.2)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 3
                    )
            )
            .background(
                Circle()
                    .fill(Color.white.opacity(0.02))
            )
            
            // Slider
            VStack(spacing: 20) {
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background track
                        RoundedRectangle(cornerRadius: trackHeight / 2)
                            .fill(Color.white.opacity(0.1))
                            .frame(height: trackHeight)
                        
                        // Active track
                        RoundedRectangle(cornerRadius: trackHeight / 2)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color(hex: "4ecdc4"),
                                        Color(hex: "44a3aa")
                                    ],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * normalizedValue, height: trackHeight)
                            .shadow(color: Color(hex: "4ecdc4").opacity(0.5), radius: 4)
                        
                        // Knob
                        Circle()
                            .fill(Color.white)
                            .frame(width: knobSize, height: knobSize)
                            .shadow(color: Color.black.opacity(0.15), radius: 4, x: 0, y: 2)
                            .overlay(
                                Circle()
                                    .fill(
                                        RadialGradient(
                                            colors: [
                                                Color.white,
                                                Color.gray.opacity(0.1)
                                            ],
                                            center: .center,
                                            startRadius: 0,
                                            endRadius: 14
                                        )
                                    )
                            )
                            .scaleEffect(isDragging ? 1.15 : 1.0)
                            .offset(x: geometry.size.width * normalizedValue - knobSize / 2)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isDragging)
                        
                        // Min/Max labels
                        HStack {
                            Text("\(range.lowerBound)")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.5))
                            
                            Spacer()
                            
                            Text("\(range.upperBound)")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.5))
                        }
                        .offset(y: 20)
                    }
                    .frame(height: 40)
                    .contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                if !isDragging {
                                    UIImpactFeedbackGenerator(style: .light).impactOccurred()
                                }
                                isDragging = true
                                
                                let newValue = Int(round((gesture.location.x / geometry.size.width) * Double(range.upperBound - range.lowerBound))) + range.lowerBound
                                value = max(range.lowerBound, min(range.upperBound, newValue))
                                
                                // Haptic feedback
                                if value != lastHapticValue {
                                    if value % 10 == 0 {
                                        UIImpactFeedbackGenerator(style: .light).impactOccurred()
                                    }
                                    lastHapticValue = value
                                }
                            }
                            .onEnded { _ in
                                isDragging = false
                                UIImpactFeedbackGenerator(style: .light).impactOccurred()
                            }
                    )
                    .onTapGesture { location in
                        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                        
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            let newValue = Int(round((location.x / geometry.size.width) * Double(range.upperBound - range.lowerBound))) + range.lowerBound
                            value = max(range.lowerBound, min(range.upperBound, newValue))
                        }
                    }
                }
                .frame(height: 40)
                
                // Quick adjustment buttons
                HStack(spacing: 12) {
                    QuickAdjustButton(value: -10, bpmValue: $value, range: range)
                    QuickAdjustButton(value: -1, bpmValue: $value, range: range)
                    QuickAdjustButton(value: 1, bpmValue: $value, range: range)
                    QuickAdjustButton(value: 10, bpmValue: $value, range: range)
                }
            }
        }
        .padding(.horizontal, 30)
        .accessibilityElement()
        .accessibilityLabel("BPM Slider")
        .accessibilityValue("\(value) beats per minute")
        .accessibilityHint("Swipe left or right to adjust tempo")
        .accessibilityAdjustableAction { direction in
            switch direction {
            case .increment:
                value = min(value + 5, range.upperBound)
                UISelectionFeedbackGenerator().selectionChanged()
            case .decrement:
                value = max(value - 5, range.lowerBound)
                UISelectionFeedbackGenerator().selectionChanged()
            @unknown default:
                break
            }
        }
        .onAppear {
            lastHapticValue = value
        }
    }
    
    private var normalizedValue: CGFloat {
        CGFloat(value - range.lowerBound) / CGFloat(range.upperBound - range.lowerBound)
    }
}

struct QuickAdjustButton: View {
    let value: Int
    @Binding var bpmValue: Int
    let range: ClosedRange<Int>
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            let newValue = bpmValue + value
            bpmValue = max(range.lowerBound, min(range.upperBound, newValue))
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
        }) {
            Text(value > 0 ? "+\(value)" : "\(value)")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color(hex: "4ecdc4"))
                .frame(maxWidth: .infinity)
                .frame(height: 44)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(hex: "4ecdc4").opacity(0.15))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "4ecdc4").opacity(0.3), lineWidth: 1)
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .onLongPressGesture(minimumDuration: .infinity, maximumDistance: .infinity,
            pressing: { pressing in
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = pressing
                }
            },
            perform: {}
        )
    }
}