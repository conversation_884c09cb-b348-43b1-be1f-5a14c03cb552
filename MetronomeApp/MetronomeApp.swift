import SwiftUI
import AVFoundation

@main
struct MetronomeApp: App {
    init() {
        configureAudioSession()
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
    
    private func configureAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            
            // 设置更适合节拍器的音频会话配置
            try audioSession.setCategory(.playback, 
                                       mode: .default, 
                                       options: [.defaultToSpeaker, .allowBluetooth])
            
            // 设置音频采样率和缓冲区大小以获得更好的延迟性能
            try audioSession.setPreferredSampleRate(44100.0)
            try audioSession.setPreferredIOBufferDuration(0.005) // 5ms缓冲区，减少延迟
            
            try audioSession.setActive(true)
            
            // Handle audio interruptions
            NotificationCenter.default.addObserver(
                forName: AVAudioSession.interruptionNotification,
                object: nil,
                queue: .main
            ) { notification in
                guard let userInfo = notification.userInfo,
                      let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
                      let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
                    return
                }
                
                switch type {
                case .began:
                    print("Audio interruption began")
                case .ended:
                    do {
                        try AVAudioSession.sharedInstance().setActive(true)
                        print("Audio session reactivated after interruption")
                    } catch {
                        print("Failed to reactivate audio session: \(error)")
                    }
                @unknown default:
                    break
                }
            }
            
            print("Audio session configured successfully")
            print("Sample rate: \(audioSession.sampleRate)")
            print("IO buffer duration: \(audioSession.ioBufferDuration)")
        } catch {
            print("Failed to configure audio session: \(error)")
        }
    }
}