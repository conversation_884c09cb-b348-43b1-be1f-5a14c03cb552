import Foundation
import AVFoundation
import Combine

extension Double {
    func clamped(to limits: ClosedRange<Double>) -> Double {
        return min(max(self, limits.lowerBound), limits.upperBound)
    }
}

extension Float {
    func clamped(to limits: ClosedRange<Float>) -> Float {
        return min(max(self, limits.lowerBound), limits.upperBound)
    }
}

extension Int {
    func clamped(to limits: ClosedRange<Int>) -> Int {
        return Swift.min(Swift.max(self, limits.lowerBound), limits.upperBound)
    }
}

// Thread-safe error handling
struct MetronomeError: Error, LocalizedError {
    let message: String
    let underlyingError: Error?
    
    init(_ message: String, underlyingError: Error? = nil) {
        self.message = message
        self.underlyingError = underlyingError
    }
    
    var errorDescription: String? {
        return message
    }
}

class MetronomeEngine: ObservableObject {
    // Thread-safe serial queue for all metronome operations
    private let metronomeQueue = DispatchQueue(label: "com.metronome.engine", qos: .userInitiated)
    private let bufferQueue = DispatchQueue(label: "com.metronome.buffer", qos: .utility)
    
    // Thread-safe state management
    @Published var bpm: Int = 120 {
        didSet {
            guard oldValue != bpm else { return }
            UserDefaults.standard.set(bpm, forKey: "metronome_bpm")
            
            metronomeQueue.async { [weak self] in
                self?.handleBPMChange(from: oldValue, to: self?.bpm ?? oldValue)
            }
        }
    }
    
    @Published var isPlaying = false {
        didSet {
            guard oldValue != isPlaying else { return }
            // No additional async work needed - UI updates are handled automatically
        }
    }
    @Published var beatsPerBar = 4 {
        didSet {
            guard oldValue != beatsPerBar else { return }
            UserDefaults.standard.set(beatsPerBar, forKey: "metronome_beats_per_bar")
            metronomeQueue.async { [weak self] in
                self?.handleBeatsPerBarChange(from: oldValue, to: self?.beatsPerBar ?? oldValue)
            }
        }
    }
    
    @Published var noteValue = 4 {
        didSet {
            guard oldValue != noteValue else { return }
            UserDefaults.standard.set(noteValue, forKey: "metronome_note_value")
        }
    }
    
    @Published var currentBeat = 0
    @Published var soundType: SoundType = .woodBlock {
        didSet {
            guard oldValue != soundType else { return }
            UserDefaults.standard.set(soundType.rawValue, forKey: "metronome_sound_type")
            metronomeQueue.async { [weak self] in
                self?.handleSoundTypeChange(from: oldValue, to: self?.soundType ?? oldValue)
            }
        }
    }
    
    @Published var rhythmPattern: RhythmPattern = .straight {
        didSet {
            guard oldValue != rhythmPattern else { return }
            UserDefaults.standard.set(rhythmPattern.rawValue, forKey: "metronome_rhythm_pattern")
            metronomeQueue.async { [weak self] in
                self?.handleRhythmPatternChange(from: oldValue, to: self?.rhythmPattern ?? oldValue)
            }
        }
    }
    
    @Published var volume: Float = 0.8 {
        didSet {
            guard oldValue != volume else { return }
            let clampedVolume = volume.clamped(to: 0.0...1.0)
            if clampedVolume != volume {
                DispatchQueue.main.async {
                    self.volume = clampedVolume
                }
                return
            }
            UserDefaults.standard.set(volume, forKey: "metronome_volume")
            metronomeQueue.async { [weak self] in
                self?.updateVolume()
            }
        }
    }
    
    let beatPublisher = PassthroughSubject<Int, Never>()
    
    private var audioEngine = AVAudioEngine()
    private var playerNode = AVAudioPlayerNode()
    private var reverb = AVAudioUnitReverb()
    private var volumeNode = AVAudioMixerNode()
    private var accentBuffer: AVAudioPCMBuffer?
    private var weakBuffer: AVAudioPCMBuffer?
    private var clickBuffer: AVAudioPCMBuffer?
    private var tickBuffer: AVAudioPCMBuffer?
    private var hiHatBuffer: AVAudioPCMBuffer?
    
    // Buffer pool for performance optimization
    private var bufferPool: [AVAudioPCMBuffer] = []
    private let maxBufferPoolSize = 20
    private var bufferFormat: AVAudioFormat?
    
    // Thread-safe internal state
    private var beatCounter = 0
    private var subBeatCounter = 0
    private var scheduledBeats = 0
    private var scheduledBufferIDs: Set<UUID> = []
    private var lookAheadTime: TimeInterval = 0.1
    private var lastScheduleTime: AVAudioTime?
    private var tapTimes: [Date] = []
    private let maxTapTimes = 8
    private var scheduleTimer: Timer?
    private var startTime: AVAudioTime?
    private var isInitialized = false
    
    // Precise timing state
    private var nextBeatTime: AVAudioTime?
    private var currentBeatStartTime: AVAudioTime?
    private var totalBeatsScheduled: Int = 0
    private var scheduleAheadTime: TimeInterval = 0.1 // 100ms lookahead to reduce load
    private var renderThread: Thread?
    
    // Error handling
    private var lastError: Error?
    
    // Buffer cache for performance
    private var bufferCache: [String: AVAudioPCMBuffer] = [:]
    private let maxCacheSize = 20
    
    // Performance monitoring
    private var bufferUnderruns = 0
    private var lastBufferGenerationTime: TimeInterval = 0
    
    enum SoundType: String, CaseIterable {
        case woodBlock = "Wood Block"
        case click = "Click"
        case tick = "Tick"
        case hiHat = "Hi-Hat"
        
        var description: String {
            return self.rawValue
        }
    }
    
    enum RhythmPattern: String, CaseIterable {
        case straight = "Straight"
        case eighth = "Eighth Notes"
        case sixteenth = "Sixteenth Notes"
        case eighthSixteenth = "Eighth-Sixteenth"
        case sixteenthEighth = "Sixteenth-Eighth"
        case triplet = "Triplet"
        case syncopated = "Syncopated"
        case sextuplet = "Sextuplet"
        case shuffle = "Shuffle"
        case dotted = "Dotted"
        case swung = "Swing"
        
        var description: String {
            switch self {
            case .straight: return "Even beats"
            case .eighth: return "2 notes per beat"
            case .sixteenth: return "4 notes per beat"
            case .eighthSixteenth: return "Eighth + Sixteenth"
            case .sixteenthEighth: return "Sixteenth + Eighth"
            case .triplet: return "Triplet rhythm"
            case .syncopated: return "Missing 3rd note"
            case .sextuplet: return "6 notes per beat"
            case .shuffle: return "Jazz swing feel"
            case .dotted: return "Dotted rhythm"
            case .swung: return "Swing rhythm"
            }
        }
    }
    
    init() {
        setupAudioSession()
        setupAudioEngine()
        loadSettings()
        initializeEngine()
        initializeBufferPool()
    }
    
    deinit {
        cleanup()
    }
    
    private func cleanup() {
        // First invalidate timer on main thread
        DispatchQueue.main.sync {
            scheduleTimer?.invalidate()
            scheduleTimer = nil
        }
        
        // Then clean up audio resources
        metronomeQueue.sync {
            // Set playing to false without triggering stop
            isPlaying = false
            
            // Cancel scheduled buffers
            if playerNode.engine != nil {
                playerNode.stop()
            }
            scheduledBufferIDs.removeAll()
            scheduledBeats = 0
            
            // Stop audio engine
            if audioEngine.isRunning {
                audioEngine.stop()
            }
            
            // Clear resources
            bufferCache.removeAll()
            bufferPool.removeAll()
            bufferFormat = nil
        }
        
        NotificationCenter.default.removeObserver(self)
    }
    
    private func loadSettings() {
        let loadedBPM = UserDefaults.standard.object(forKey: "metronome_bpm") as? Int ?? 120
        let loadedBeatsPerBar = UserDefaults.standard.object(forKey: "metronome_beats_per_bar") as? Int ?? 4
        let loadedNoteValue = UserDefaults.standard.object(forKey: "metronome_note_value") as? Int ?? 4
        let loadedVolume = UserDefaults.standard.object(forKey: "metronome_volume") as? Float ?? 0.8
        
        // Validate and clamp values
        DispatchQueue.main.async {
            self.bpm = loadedBPM.clamped(to: 30...300)
            self.beatsPerBar = loadedBeatsPerBar.clamped(to: 2...8)
            self.noteValue = [2, 4, 8, 16].contains(loadedNoteValue) ? loadedNoteValue : 4
            self.volume = loadedVolume.clamped(to: 0.0...1.0)
        }
        
        if let soundTypeString = UserDefaults.standard.string(forKey: "metronome_sound_type"),
           let savedSoundType = SoundType(rawValue: soundTypeString) {
            DispatchQueue.main.async {
                self.soundType = savedSoundType
            }
        }
        
        if let rhythmString = UserDefaults.standard.string(forKey: "metronome_rhythm_pattern"),
           let savedRhythm = RhythmPattern(rawValue: rhythmString) {
            DispatchQueue.main.async {
                self.rhythmPattern = savedRhythm
            }
        }
    }
    
    private func initializeEngine() {
        // Generate initial sounds on a background queue to avoid blocking
        bufferQueue.async { [weak self] in
            self?.generateSoundsForCurrentType()
        }
    }
    
    // MARK: - Buffer Pool Management
    
    private func initializeBufferPool() {
        metronomeQueue.async { [weak self] in
            guard let self = self else { return }
            
            let sampleRate = 44100.0
            guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 2) else { return }
            
            self.bufferFormat = format
            
            // Pre-allocate buffers for better performance
            for _ in 0..<10 {
                if let buffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: AVAudioFrameCount(sampleRate * 0.2)) {
                    self.bufferPool.append(buffer)
                }
            }
        }
    }
    
    private func getBufferFromPool() -> AVAudioPCMBuffer? {
        // Avoid sync dispatch which can cause deadlocks
        if !bufferPool.isEmpty {
            return bufferPool.removeLast()
        } else if let format = bufferFormat {
            // Create new buffer if pool is empty
            return AVAudioPCMBuffer(pcmFormat: format, frameCapacity: AVAudioFrameCount(44100.0 * 0.2))
        }
        return nil
    }
    
    private func returnBufferToPool(_ buffer: AVAudioPCMBuffer) {
        metronomeQueue.async { [weak self] in
            guard let self = self else { return }
            
            if self.bufferPool.count < self.maxBufferPoolSize {
                buffer.frameLength = 0 // Reset buffer
                self.bufferPool.append(buffer)
            }
        }
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            
            try audioSession.setCategory(.playback,
                                       mode: .default,
                                       options: [.allowBluetooth, .mixWithOthers])
            
            try audioSession.setPreferredSampleRate(44100.0)
            try audioSession.setPreferredIOBufferDuration(0.02) // 20ms buffer for stability
            
            try audioSession.setActive(true)
            
            // Handle interruptions
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleAudioInterruption),
                name: AVAudioSession.interruptionNotification,
                object: nil
            )
            
            print("Audio session configured successfully")
        } catch {
            lastError = MetronomeError("Audio session configuration failed", underlyingError: error)
            print("Failed to configure audio session: \(error)")
        }
    }
    
    @objc private func handleAudioInterruption(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        metronomeQueue.async { [weak self] in
            switch type {
            case .began:
                if self?.isPlaying == true {
                    DispatchQueue.main.async {
                        self?.stop()
                    }
                }
            case .ended:
                if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                    let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                    if options.contains(.shouldResume) {
                        do {
                            try AVAudioSession.sharedInstance().setActive(true)
                        } catch {
                            self?.lastError = MetronomeError("Failed to reactivate audio session", underlyingError: error)
                        }
                    }
                }
            @unknown default:
                break
            }
        }
    }
    
    private func setupAudioEngine() {
        metronomeQueue.async { [weak self] in
            self?.setupAudioEngineSync()
        }
    }
    
    private func setupAudioEngineSync() {
        guard !isInitialized else { 
            print("✅ Audio engine already initialized")
            return 
        }
        
        print("🔧 Setting up audio engine synchronously on thread: \(Thread.current)")
        
        // Detach nodes if they're already attached to prevent multiple attachments
        if playerNode.engine != nil {
            print("🔌 Detaching existing playerNode")
            audioEngine.detach(playerNode)
        }
        if volumeNode.engine != nil {
            print("🔌 Detaching existing volumeNode")
            audioEngine.detach(volumeNode)
        }
        
        // Setup audio engine with low-latency configuration (removed reverb to reduce load)
        print("🔗 Attaching audio nodes...")
        audioEngine.attach(playerNode)
        audioEngine.attach(volumeNode)
        
        // Create audio connections with optimal format
        let format = audioEngine.outputNode.outputFormat(forBus: 0)
        audioEngine.connect(playerNode, to: volumeNode, format: format)
        audioEngine.connect(volumeNode, to: audioEngine.mainMixerNode, format: format)
        
        updateVolume()
        
        do {
            print("🚀 Starting audio engine...")
            if !audioEngine.isRunning {
                try audioEngine.start()
                print("✅ Audio engine started")
            } else {
                print("ℹ️ Audio engine already running")
            }
            
            print("▶️ Starting player node...")
            if !playerNode.isPlaying {
                playerNode.play()
                print("✅ Player node started")
            } else {
                print("ℹ️ Player node already playing")
            }
            
            isInitialized = true
            print("🎉 Audio engine initialized successfully")
        } catch {
            lastError = MetronomeError("Audio engine start failed", underlyingError: error)
            print("❌ Failed to start audio engine: \(error)")
            DispatchQueue.main.async {
                self.isPlaying = false
            }
        }
    }
    
    private func updateVolume() {
        // Clamped volume to prevent clipping
        let safeVolume = volume.clamped(to: 0.0...1.0)
        volumeNode.outputVolume = safeVolume // Direct volume without amplification
    }
    
    // MARK: - Thread-Safe State Change Handlers
    
    private func handleBPMChange(from oldBPM: Int, to newBPM: Int) {
        // Regenerate sounds for new timing
        bufferQueue.async { [weak self] in
            self?.generateSoundsForCurrentType()
        }
        
        // If playing, smoothly transition to new tempo
        if isPlaying {
            cancelScheduledBuffers()
            scheduleBeatsIfNeeded()
        }
    }
    
    private func handleBeatsPerBarChange(from oldValue: Int, to newValue: Int) {
        // Smooth transition: adjust current beat if needed
        if isPlaying {
            let currentRatio = Double(currentBeat) / Double(oldValue)
            let newBeat = max(1, min(newValue, Int(currentRatio * Double(newValue))))
            
            DispatchQueue.main.async {
                self.currentBeat = newBeat
            }
        }
    }
    
    private func handleSoundTypeChange(from oldType: SoundType, to newType: SoundType) {
        bufferQueue.async { [weak self] in
            self?.generateSoundsForCurrentType()
        }
        
        // If playing, provide immediate audio feedback
        if isPlaying {
            playPreviewSound()
        }
    }
    
    private func handleRhythmPatternChange(from oldPattern: RhythmPattern, to newPattern: RhythmPattern) {
        if isPlaying {
            // Smart rhythm transition: complete current beat, then switch
            let wasInMiddleOfBeat = subBeatCounter > 0
            
            if !wasInMiddleOfBeat {
                // If on main beat, immediately switch
                subBeatCounter = 0
                beatCounter = (currentBeat - 1) % beatsPerBar
            }
            // Otherwise, let current sub-beat pattern complete naturally
        }
    }
    
    private func playPreviewSound() {
        guard let accentBuffer = accentBuffer,
              audioEngine.isRunning,
              playerNode.isPlaying else { return }
        
        let sampleRate = audioEngine.outputNode.outputFormat(forBus: 0).sampleRate
        let previewTime = AVAudioTime(sampleTime: AVAudioFramePosition(0.02 * sampleRate), atRate: sampleRate)
        
        playerNode.scheduleBuffer(accentBuffer, at: previewTime)
    }
    
    private func generateSoundsForCurrentType() {
        let startTime = CACurrentMediaTime()
        
        // Check cache first
        let cacheKey = "\(soundType.rawValue)-\(bpm)"
        if let cachedAccent = bufferCache["\(cacheKey)-accent"],
           let cachedWeak = bufferCache["\(cacheKey)-weak"] {
            accentBuffer = cachedAccent
            weakBuffer = cachedWeak
            print("Using cached buffers for \(soundType.rawValue) at \(bpm) BPM")
            return
        }
        
        // Generate new buffers
        switch soundType {
        case .woodBlock:
            generateWoodBlockSounds()
        case .click:
            generateClickSounds()
        case .tick:
            generateTickSounds()
        case .hiHat:
            generateHiHatSounds()
        }
        
        // Cache the generated buffers
        if let accent = accentBuffer, let weak = weakBuffer {
            bufferCache["\(cacheKey)-accent"] = accent
            bufferCache["\(cacheKey)-weak"] = weak
            
            // Limit cache size
            if bufferCache.count > maxCacheSize * 2 {
                // Remove oldest entries
                let keysToRemove = Array(bufferCache.keys.prefix(10))
                keysToRemove.forEach { bufferCache.removeValue(forKey: $0) }
            }
        }
        
        lastBufferGenerationTime = CACurrentMediaTime() - startTime
        print("Generated buffers for \(soundType.rawValue) at \(bpm) BPM in \(lastBufferGenerationTime * 1000)ms")
    }
    
    private func generateWoodBlockSounds() {
        let sampleRate = 44100.0
        // 简化的时长计算
        let duration = 0.08 // 固定80ms时长，减少计算
        let frameCount = AVAudioFrameCount(sampleRate * duration)
        
        guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 2) else { return }
        
        accentBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        weakBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        
        guard let accentBuffer = accentBuffer,
              let weakBuffer = weakBuffer else { return }
        
        accentBuffer.frameLength = frameCount
        weakBuffer.frameLength = frameCount
        
        // 预计算常量以提高性能
        let invDuration = 1.0 / duration
        let invSampleRate = 1.0 / sampleRate
        
        for frame in 0..<Int(frameCount) {
            let time = Double(frame) * invSampleRate
            let normalizedTime = time * invDuration
            
            // 简化的包络
            let envelope = exp(-normalizedTime * 30.0)
            
            // 简化的木块声音 - 只使用主要组件
            let fundamental = 800.0
            let tone = sin(2.0 * .pi * fundamental * time)
            let click = exp(-normalizedTime * 200.0) * sin(2.0 * .pi * 2400.0 * time) * 0.3
            
            // 重音
            let strongSound = (tone + click) * envelope * 0.8
            
            // 弱音 - 使用相同计算，只改变音量
            let weakSound = (tone + click) * envelope * 0.4
            
            // 直接写入缓冲区，避免额外变量
            let strongFloat = Float(strongSound)
            let weakFloat = Float(weakSound)
            
            accentBuffer.floatChannelData?[0][frame] = strongFloat
            accentBuffer.floatChannelData?[1][frame] = strongFloat
            weakBuffer.floatChannelData?[0][frame] = weakFloat
            weakBuffer.floatChannelData?[1][frame] = weakFloat
        }
    }
    
    private func generateClickSounds() {
        let sampleRate = 44100.0
        // 点击声音的动态时长调整
        let maxDuration = 0.05 // 最大50ms
        let minDuration = 0.02 // 最小20ms
        let beatInterval = 60.0 / Double(bpm)
        let dynamicDuration = min(maxDuration, max(minDuration, beatInterval * 0.3))
        let frameCount = AVAudioFrameCount(sampleRate * dynamicDuration)
        
        guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 2) else { return }
        
        accentBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        weakBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        
        guard let accentBuffer = accentBuffer,
              let weakBuffer = weakBuffer else { return }
        
        accentBuffer.frameLength = frameCount
        weakBuffer.frameLength = frameCount
        
        for frame in 0..<Int(frameCount) {
            let time = Double(frame) / sampleRate
            let normalizedTime = time / dynamicDuration
            let envelope = exp(-normalizedTime * 60.0) // 更快的衰减
            
            // High-pitched click
            let frequency = 2000.0
            let click = sin(2.0 * .pi * frequency * time) * envelope
            
            for channel in 0..<2 {
                accentBuffer.floatChannelData?[channel][frame] = Float(click * 1.6)
                weakBuffer.floatChannelData?[channel][frame] = Float(click * 0.8)
            }
        }
    }
    
    private func generateTickSounds() {
        let sampleRate = 44100.0
        // Tick声音的动态时长调整
        let maxDuration = 0.08 // 最大80ms
        let minDuration = 0.03 // 最小30ms
        let beatInterval = 60.0 / Double(bpm)
        let dynamicDuration = min(maxDuration, max(minDuration, beatInterval * 0.35))
        let frameCount = AVAudioFrameCount(sampleRate * dynamicDuration)
        
        guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 2) else { return }
        
        accentBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        weakBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        
        guard let accentBuffer = accentBuffer,
              let weakBuffer = weakBuffer else { return }
        
        accentBuffer.frameLength = frameCount
        weakBuffer.frameLength = frameCount
        
        for frame in 0..<Int(frameCount) {
            let time = Double(frame) / sampleRate
            let normalizedTime = time / dynamicDuration
            let envelope = exp(-normalizedTime * 35.0) // 更快的衰减
            
            // Traditional metronome tick
            let frequency = 800.0
            let tick = sin(2.0 * .pi * frequency * time) * envelope
            
            for channel in 0..<2 {
                accentBuffer.floatChannelData?[channel][frame] = Float(tick * 1.6)
                weakBuffer.floatChannelData?[channel][frame] = Float(tick * 0.8)
            }
        }
    }
    
    private func generateHiHatSounds() {
        let sampleRate = 44100.0
        // Hi-Hat声音的动态时长调整
        let maxDuration = 0.1 // 最大100ms
        let minDuration = 0.04 // 最小40ms
        let beatInterval = 60.0 / Double(bpm)
        let dynamicDuration = min(maxDuration, max(minDuration, beatInterval * 0.35))
        let frameCount = AVAudioFrameCount(sampleRate * dynamicDuration)
        
        guard let format = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 2) else { return }
        
        accentBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        weakBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount)
        
        guard let accentBuffer = accentBuffer,
              let weakBuffer = weakBuffer else { return }
        
        accentBuffer.frameLength = frameCount
        weakBuffer.frameLength = frameCount
        
        for frame in 0..<Int(frameCount) {
            let time = Double(frame) / sampleRate
            let normalizedTime = time / dynamicDuration
            let envelope = exp(-normalizedTime * 45.0) // 更快的衰减
            
            // Hi-hat simulation with noise and high frequencies
            let noise = Double.random(in: -1...1)
            let highFreq = sin(2.0 * .pi * 8000.0 * time) * 0.3
            let hihat = (noise * 0.7 + highFreq) * envelope
            
            for channel in 0..<2 {
                accentBuffer.floatChannelData?[channel][frame] = Float(hihat * 1.2)
                weakBuffer.floatChannelData?[channel][frame] = Float(hihat * 0.6)
            }
        }
    }
    
    func start() {
        print("🎵 Start method called, isPlaying: \(isPlaying), thread: \(Thread.current)")
        
        // Prevent multiple simultaneous start calls
        guard !isPlaying else {
            print("⚠️ Metronome is already playing")
            return
        }
        
        // Update UI state immediately to prevent multiple clicks
        print("📱 Setting isPlaying = true on main thread")
        DispatchQueue.main.async {
            self.isPlaying = true
        }
        
        metronomeQueue.async { [weak self] in
            print("🔄 Inside metronome queue, thread: \(Thread.current)")
            guard let self = self else { 
                print("❌ Self is nil, resetting isPlaying")
                DispatchQueue.main.async {
                    self?.isPlaying = false
                }
                return 
            }
            
            // Initialize if needed
            if !self.isInitialized {
                print("Initializing audio engine...")
                self.setupAudioEngineSync()
            }
            
            guard self.isInitialized else {
                print("Failed to initialize audio engine")
                self.lastError = MetronomeError("Audio engine not initialized")
                DispatchQueue.main.async {
                    self.isPlaying = false
                }
                return
            }
            
            print("Starting metronome...")
            
            // Ensure audio engine is running
            if !self.audioEngine.isRunning {
                do {
                    try self.audioEngine.start()
                    print("Audio engine started successfully")
                } catch {
                    self.lastError = MetronomeError("Failed to start audio engine", underlyingError: error)
                    print("Error starting audio engine: \(error)")
                    DispatchQueue.main.async {
                        self.isPlaying = false
                    }
                    return
                }
            }
            
            // Ensure player node is playing
            if !self.playerNode.isPlaying {
                self.playerNode.play()
                print("Player node started")
            }
            
            // Generate audio buffers if needed (but don't block)
            if self.accentBuffer == nil {
                print("Audio buffers not ready, generating...")
                // Generate synchronously on this queue to ensure they're ready
                self.generateSoundsForCurrentType()
            }
            
            // Final check for buffers
            guard self.accentBuffer != nil else {
                print("Failed to generate audio buffers")
                self.lastError = MetronomeError("Audio buffers not available")
                DispatchQueue.main.async {
                    self.isPlaying = false
                }
                return
            }
            
            // Cancel any existing scheduled buffers
            self.cancelScheduledBuffers()
            
            // Reset state
            self.beatCounter = 0
            self.subBeatCounter = 0
            self.scheduledBeats = 0
            self.totalBeatsScheduled = 0
            self.lastScheduleTime = nil
            
            let format = self.audioEngine.outputNode.outputFormat(forBus: 0)
            let sampleRate = format.sampleRate
            
            // Set precise start time
            let now = self.playerNode.lastRenderTime ?? AVAudioTime(sampleTime: 0, atRate: sampleRate)
            self.startTime = AVAudioTime(sampleTime: now.sampleTime + AVAudioFramePosition(0.01 * sampleRate), atRate: sampleRate)
            self.nextBeatTime = self.startTime
            self.currentBeatStartTime = self.startTime
            
            // Update UI state
            DispatchQueue.main.async {
                self.currentBeat = 0
            }
            
            print("Setting up playback with BPM: \(self.bpm)")
            
            // Schedule initial beats with precise timing
            self.scheduleInitialBeatsV2()
            
            // Use CADisplayLink for precise scheduling instead of Timer
            DispatchQueue.main.async {
                self.scheduleTimer?.invalidate()
                self.scheduleTimer = Timer.scheduledTimer(withTimeInterval: 0.005, repeats: true) { [weak self] timer in
                    guard let self = self else {
                        timer.invalidate()
                        return
                    }
                    
                    self.metronomeQueue.async {
                        guard self.isPlaying else {
                            DispatchQueue.main.async {
                                timer.invalidate()
                            }
                            return
                        }
                        self.scheduleBeatsIfNeeded()
                    }
                }
            }
            
            print("Metronome started successfully")
        }
    }
    
    private func scheduleInitialBeatsV2() {
        guard let startTime = startTime else { return }
        
        let format = audioEngine.outputNode.outputFormat(forBus: 0)
        let sampleRate = format.sampleRate
        let baseInterval = 60.0 / Double(bpm)
        
        // First dot lights up immediately
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.currentBeat = 1
            self.beatPublisher.send(1)
        }
        
        // Schedule initial batch of beats with precise timing
        var currentTime = startTime
        var tempBeatCounter = 0
        var tempSubBeatCounter = 0
        
        // Pre-calculate and schedule multiple beats
        let beatsToSchedule = 8 // Reduced to prevent overload
        
        for _ in 0..<beatsToSchedule {
            let (buffer, interval) = getBufferAndIntervalForRhythmPattern(
                baseInterval: baseInterval,
                beatIndex: tempBeatCounter,
                subBeatIndex: tempSubBeatCounter
            )
            
            if let audioBuffer = buffer {
                let isMainBeat = (tempSubBeatCounter == 0)
                let beatNumber = (tempBeatCounter % beatsPerBar) + 1
                let scheduleFrame = currentTime.sampleTime
                
                // Create a unique identifier for this scheduled beat
                let beatID = UUID()
                scheduledBufferIDs.insert(beatID)
                
                playerNode.scheduleBuffer(audioBuffer, at: currentTime) { [weak self] in
                    self?.metronomeQueue.async {
                        guard let self = self else { return }
                        self.scheduledBufferIDs.remove(beatID)
                        self.scheduledBeats = max(0, self.scheduledBeats - 1)
                    }
                }
                
                // Schedule UI update based on audio time
                if isMainBeat {
                    let audioLatency = AVAudioSession.sharedInstance().outputLatency
                    let uiUpdateTime = Double(scheduleFrame) / sampleRate - audioLatency
                    let currentSystemTime = CACurrentMediaTime()
                    let delay = max(0, uiUpdateTime - currentSystemTime)
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
                        guard let self = self, self.isPlaying else { return }
                        self.currentBeat = beatNumber
                        self.beatPublisher.send(beatNumber)
                    }
                }
                
                scheduledBeats += 1
                totalBeatsScheduled += 1
            }
            
            // Update counters precisely
            let subBeatsPerBeat = getSubBeatsPerBeat(for: rhythmPattern)
            tempSubBeatCounter += 1
            if tempSubBeatCounter >= subBeatsPerBeat {
                tempSubBeatCounter = 0
                tempBeatCounter += 1
            }
            
            // Move to next beat time
            currentTime = AVAudioTime(
                sampleTime: currentTime.sampleTime + AVAudioFramePosition(interval * sampleRate),
                atRate: sampleRate
            )
        }
        
        // Store state for next scheduling
        beatCounter = tempBeatCounter
        subBeatCounter = tempSubBeatCounter
        lastScheduleTime = currentTime
        
        print("Scheduled initial \(scheduledBeats) beats with precise timing")
    }
    
    
    func stop() {
        // Update UI state immediately to prevent race conditions
        DispatchQueue.main.async { [weak self] in
            self?.isPlaying = false
            self?.currentBeat = 0
            
            // Properly invalidate and clean up timer
            self?.scheduleTimer?.invalidate()
            self?.scheduleTimer = nil
        }
        
        metronomeQueue.async { [weak self] in
            guard let self = self else { return }
            
            // Cancel all scheduled buffers
            self.cancelScheduledBuffers()
            
            // Reset state completely
            self.beatCounter = 0
            self.subBeatCounter = 0
            self.scheduledBeats = 0
            self.totalBeatsScheduled = 0
            self.lastScheduleTime = nil
            self.startTime = nil
            self.nextBeatTime = nil
            self.currentBeatStartTime = nil
            
            // Reset player node only if it's attached
            if self.playerNode.engine != nil {
                self.playerNode.stop()
                
                // Restart player node for next use if engine is running
                if self.audioEngine.isRunning {
                    self.playerNode.play()
                }
            }
            
            print("Metronome stopped successfully")
        }
    }
    
    private func cancelScheduledBuffers() {
        // Check if player node is attached before stopping
        if playerNode.engine != nil {
            playerNode.stop()
        }
        scheduledBufferIDs.removeAll()
        scheduledBeats = 0
        
        // Only play if engine is running and node is attached
        if audioEngine.isRunning && playerNode.engine != nil {
            playerNode.play()
        }
    }
    
    private func smoothRhythmTransition() {
        // 保存当前的播放状态
        let currentMainBeat = currentBeat
        let wasInMiddleOfBeat = subBeatCounter > 0
        
        // 智能地处理节奏型切换
        if wasInMiddleOfBeat {
            // 如果在节拍中间，等待到下一个主节拍再切换
            // 这样可以保持音乐的连贯性
            waitForNextBeatToTransition()
        } else {
            // 如果在主节拍上，立即切换
            immediateRhythmTransition(currentMainBeat: currentMainBeat)
        }
    }
    
    private func waitForNextBeatToTransition() {
        // 标记需要在下一个主节拍时切换节奏型
        // 这个方法会让当前的sub-beat模式完成，然后在下一个主节拍开始新的节奏型
        // 实际上不需要特殊处理，因为scheduleNextBeats会自动处理
    }
    
    private func immediateRhythmTransition(currentMainBeat: Int) {
        // 重置计数器为新的节奏型模式
        subBeatCounter = 0
        beatCounter = (currentMainBeat - 1) % beatsPerBar
        
        // 立即播放一个节拍以提供即时反馈
        if let accentBuffer = accentBuffer {
            let sampleRate = audioEngine.outputNode.outputFormat(forBus: 0).sampleRate
            let currentTime = playerNode.lastRenderTime ?? AVAudioTime(sampleTime: 0, atRate: sampleRate)
            
            // 稍微延迟以避免音频冲突
            let transitionDelay = 0.02 // 20ms延迟
            let transitionTime = AVAudioTime(
                sampleTime: currentTime.sampleTime + AVAudioFramePosition(transitionDelay * sampleRate), 
                atRate: sampleRate
            )
            
            playerNode.scheduleBuffer(accentBuffer, at: transitionTime)
            
            // 更新时间安排
            let baseInterval = 60.0 / Double(bpm)
            let subBeatsPerBeat = getSubBeatsPerBeat(for: rhythmPattern)
            let nextInterval = baseInterval / Double(subBeatsPerBeat)
            
            lastScheduleTime = AVAudioTime(
                sampleTime: transitionTime.sampleTime + AVAudioFramePosition(nextInterval * sampleRate),
                atRate: sampleRate
            )
            
            // 发送节拍事件以同步UI
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.beatPublisher.send(currentMainBeat)
            }
        }
        
        // 继续安排后续的节拍
        scheduleBeatsIfNeeded()
    }
    
    private func seamlessSoundTransition() {
        // 预先生成新的声音缓冲区
        let oldAccentBuffer = accentBuffer
        let oldWeakBuffer = weakBuffer
        
        // 生成新的声音类型
        generateSoundsForCurrentType()
        
        // 播放一个预览音以提供即时反馈
        if let newAccentBuffer = accentBuffer {
            let sampleRate = audioEngine.outputNode.outputFormat(forBus: 0).sampleRate
            let currentTime = playerNode.lastRenderTime ?? AVAudioTime(sampleTime: 0, atRate: sampleRate)
            
            // 立即播放一个新声音的预览
            let previewTime = AVAudioTime(
                sampleTime: currentTime.sampleTime + AVAudioFramePosition(0.01 * sampleRate), // 10ms延迟
                atRate: sampleRate
            )
            
            // 播放一个轻柔的预览音
            playerNode.scheduleBuffer(newAccentBuffer, at: previewTime) { [weak self] in
                // 清理旧的音频缓冲区引用
                _ = oldAccentBuffer
                _ = oldWeakBuffer
            }
        }
        
        // 发送一个轻微的UI反馈
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            // 可以在这里添加声音切换的视觉反馈
        }
    }
    
    private func smoothBeatsPerBarTransition() {
        // Adjust current beat to fit new beats per bar
        if currentBeat > beatsPerBar {
            currentBeat = 1
            beatCounter = 0
        }
        // Ensure beat counter is within valid range
        beatCounter = beatCounter % beatsPerBar
    }
    
    private func scheduleBeatsIfNeeded() {
        guard isPlaying,
              let lastTime = lastScheduleTime,
              let playerTime = playerNode.lastRenderTime else { return }
        
        let format = audioEngine.outputNode.outputFormat(forBus: 0)
        let sampleRate = format.sampleRate
        let baseInterval = 60.0 / Double(bpm)
        
        // Calculate how far ahead we should schedule
        let currentPlayTime = playerTime.sampleTime
        let lastScheduledTime = lastTime.sampleTime
        let scheduleAheadFrames = AVAudioFramePosition(scheduleAheadTime * sampleRate)
        
        // Only schedule if we're within the lookahead window
        guard lastScheduledTime - currentPlayTime < scheduleAheadFrames else { return }
        
        // Schedule beats to maintain buffer
        var currentTime = lastTime
        var tempBeatCounter = beatCounter
        var tempSubBeatCounter = subBeatCounter
        let targetScheduledBeats = 20
        
        while scheduledBeats < targetScheduledBeats {
            let (buffer, interval) = getBufferAndIntervalForRhythmPattern(
                baseInterval: baseInterval,
                beatIndex: tempBeatCounter,
                subBeatIndex: tempSubBeatCounter
            )
            
            if let audioBuffer = buffer {
                let isMainBeat = (tempSubBeatCounter == 0)
                let beatNumber = (tempBeatCounter % beatsPerBar) + 1
                let beatID = UUID()
                
                // Ensure we don't schedule in the past
                if currentTime.sampleTime > currentPlayTime {
                    scheduledBufferIDs.insert(beatID)
                    
                    playerNode.scheduleBuffer(audioBuffer, at: currentTime) { [weak self] in
                        self?.metronomeQueue.async {
                            guard let self = self else { return }
                            self.scheduledBufferIDs.remove(beatID)
                            self.scheduledBeats = max(0, self.scheduledBeats - 1)
                        }
                    }
                    
                    // Precise UI update scheduling
                    if isMainBeat {
                        scheduleUIUpdate(for: beatNumber, at: currentTime)
                    }
                    
                    scheduledBeats += 1
                    totalBeatsScheduled += 1
                }
            }
            
            // Advance counters atomically
            let subBeatsPerBeat = getSubBeatsPerBeat(for: rhythmPattern)
            tempSubBeatCounter = (tempSubBeatCounter + 1) % subBeatsPerBeat
            if tempSubBeatCounter == 0 {
                tempBeatCounter += 1
            }
            
            // Calculate next time precisely
            currentTime = AVAudioTime(
                sampleTime: currentTime.sampleTime + AVAudioFramePosition(interval * sampleRate),
                atRate: sampleRate
            )
        }
        
        // Update state atomically
        beatCounter = tempBeatCounter
        subBeatCounter = tempSubBeatCounter
        lastScheduleTime = currentTime
    }
    
    private func scheduleUIUpdate(for beat: Int, at audioTime: AVAudioTime) {
        let format = audioEngine.outputNode.outputFormat(forBus: 0)
        let sampleRate = format.sampleRate
        let audioLatency = AVAudioSession.sharedInstance().outputLatency
        
        // Convert audio time to system time
        let audioTimeInSeconds = Double(audioTime.sampleTime) / sampleRate
        let currentSystemTime = CACurrentMediaTime()
        
        // Calculate precise UI update time
        let uiUpdateTime = audioTimeInSeconds - audioLatency
        let delay = max(0, uiUpdateTime - currentSystemTime)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
            guard let self = self, self.isPlaying else { return }
            self.currentBeat = beat
            self.beatPublisher.send(beat)
        }
    }
    
    private func getSubBeatsPerBeat(for pattern: RhythmPattern) -> Int {
        switch pattern {
        case .straight: return 1
        case .eighth: return 2
        case .sixteenth: return 4
        case .eighthSixteenth, .sixteenthEighth: return 3
        case .triplet: return 3
        case .syncopated: return 4  // Four sixteenth notes (with one missing)
        case .sextuplet: return 6
        case .shuffle, .dotted, .swung: return 2
        }
    }
    
    
    private func getBufferAndIntervalForRhythmPattern(
        baseInterval: Double,
        beatIndex: Int,
        subBeatIndex: Int
    ) -> (buffer: AVAudioPCMBuffer?, interval: Double) {
        // 精确计算重音位置：只在整个小节的第一个音
        // beatIndex是总拍数，需要对beatsPerBar取模来确定是否是第一拍
        let beatInBar = beatIndex % beatsPerBar
        let isAccent = (beatInBar == 0) && (subBeatIndex == 0)
        let isFirstNoteOfBeat = (subBeatIndex == 0)
        
        // Debug logging for accent tracking
        if isAccent {
            print("ACCENT at beatIndex: \(beatIndex), beatInBar: \(beatInBar), subBeatIndex: \(subBeatIndex)")
        }
        
        switch rhythmPattern {
        case .straight:
            // 每拍一个音符
            let buffer = isAccent ? accentBuffer : weakBuffer
            return (buffer, baseInterval)
            
        case .eighth:
            // 每拍两个八分音符：只有第一拍第一个音是重音
            let eighthInterval = baseInterval / 2.0
            let buffer = isAccent ? accentBuffer : weakBuffer
            return (buffer, eighthInterval)
            
        case .sixteenth:
            // 每拍四个十六分音符：只有第一拍第一个音是重音
            let sixteenthInterval = baseInterval / 4.0
            let buffer = isAccent ? accentBuffer : weakBuffer
            return (buffer, sixteenthInterval)
            
        case .eighthSixteenth:
            // 八分+两个十六分：只有第一拍第一个音是重音
            switch subBeatIndex {
            case 0: // 八分音符
                return (isAccent ? accentBuffer : weakBuffer, baseInterval * 0.5)
            case 1: // 第一个十六分
                return (weakBuffer, baseInterval * 0.25)
            case 2: // 第二个十六分
                return (weakBuffer, baseInterval * 0.25)
            default:
                return (weakBuffer, baseInterval * 0.25)
            }
            
        case .sixteenthEighth:
            // 两个十六分+八分：只有第一拍第一个音是重音
            switch subBeatIndex {
            case 0: // 第一个十六分
                return (isAccent ? accentBuffer : weakBuffer, baseInterval * 0.25)
            case 1: // 第二个十六分
                return (weakBuffer, baseInterval * 0.25)
            case 2: // 八分音符
                return (weakBuffer, baseInterval * 0.5)
            default:
                return (weakBuffer, baseInterval * 0.25)
            }
            
        case .triplet:
            // 三连音：只有第一拍第一个音是重音
            let tripletInterval = baseInterval / 3.0
            let buffer = isAccent ? accentBuffer : weakBuffer
            return (buffer, tripletInterval)
            
        case .syncopated:
            // 切分音：1-2-X-4 (第三个位置静音)，只有第一拍第一个音是重音
            let sixteenthInterval = baseInterval / 4.0
            switch subBeatIndex {
            case 0: // 第一个十六分
                return (isAccent ? accentBuffer : weakBuffer, sixteenthInterval)
            case 1: // 第二个十六分 - 弱音
                return (weakBuffer, sixteenthInterval)
            case 2: // 第三个位置静音
                return (nil, sixteenthInterval)
            case 3: // 第四个十六分 - 弱音
                return (weakBuffer, sixteenthInterval)
            default:
                return (weakBuffer, sixteenthInterval)
            }
            
        case .sextuplet:
            // 六连音：只有第一拍第一个音是重音
            let sextupletInterval = baseInterval / 6.0
            let buffer = isAccent ? accentBuffer : weakBuffer
            return (buffer, sextupletInterval)
            
        case .shuffle:
            // 摇摆节奏：长-短 (2:1比例)，只有第一拍第一个音是重音
            switch subBeatIndex {
            case 0: // 长音符
                return (isAccent ? accentBuffer : weakBuffer, baseInterval * 2.0/3.0)
            case 1: // 短音符
                return (weakBuffer, baseInterval * 1.0/3.0)
            default:
                return (weakBuffer, baseInterval * 0.5)
            }
            
        case .dotted:
            // 附点节奏：长-短 (3:1比例)，只有第一拍第一个音是重音
            switch subBeatIndex {
            case 0: // 附点音符
                return (isAccent ? accentBuffer : weakBuffer, baseInterval * 0.75)
            case 1: // 短音符
                return (weakBuffer, baseInterval * 0.25)
            default:
                return (weakBuffer, baseInterval * 0.5)
            }
            
        case .swung:
            // 轻微摇摆：60-40分割，只有第一拍第一个音是重音
            switch subBeatIndex {
            case 0: // 稍长的音符
                return (isAccent ? accentBuffer : weakBuffer, baseInterval * 0.6)
            case 1: // 稍短的音符
                return (weakBuffer, baseInterval * 0.4)
            default:
                return (weakBuffer, baseInterval * 0.5)
            }
        }
    }
    
    func cycleBeatsPerBar() {
        let options = [2, 3, 4, 5, 6, 7, 8]
        if let currentIndex = options.firstIndex(of: beatsPerBar) {
            beatsPerBar = options[(currentIndex + 1) % options.count]
        }
    }
    
    func cycleNoteValue() {
        let options = [2, 4, 8, 16]
        if let currentIndex = options.firstIndex(of: noteValue) {
            noteValue = options[(currentIndex + 1) % options.count]
        }
    }
    
    func tapTempo() {
        let now = Date()
        tapTimes.append(now)
        
        // Keep only recent taps (within 3 seconds)
        tapTimes = tapTimes.filter { now.timeIntervalSince($0) < 3.0 }
        
        // Need at least 2 taps to calculate tempo
        if tapTimes.count >= 2 {
            let intervals = zip(tapTimes.dropFirst(), tapTimes).map { $0.0.timeIntervalSince($0.1) }
            let averageInterval = intervals.reduce(0, +) / Double(intervals.count)
            let detectedBPM = Int(round(60.0 / averageInterval))
            
            // Only update if BPM is reasonable (40-200)
            if detectedBPM >= 40 && detectedBPM <= 200 {
                bpm = detectedBPM
            }
        }
        
        // Clear old taps if we have too many
        if tapTimes.count > maxTapTimes {
            tapTimes.removeFirst()
        }
    }
    
    func resetTapTempo() {
        tapTimes.removeAll()
    }
}