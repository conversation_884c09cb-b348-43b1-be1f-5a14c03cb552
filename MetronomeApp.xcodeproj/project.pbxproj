// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		D1A384692DFE952A008BF38F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = D1A384532DFE9528008BF38F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D1A3845A2DFE9528008BF38F;
			remoteInfo = "MetronomeApp";
		};
		D1A384732DFE952A008BF38F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = D1A384532DFE9528008BF38F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D1A3845A2DFE9528008BF38F;
			remoteInfo = "MetronomeApp";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		D1A3845B2DFE9528008BF38F /* MetronomeApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "MetronomeApp.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		D1A384682DFE952A008BF38F /* MetronomeAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "MetronomeAppTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		D1A384722DFE952A008BF38F /* MetronomeAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "MetronomeAppUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		D1A384952DFE9623008BF38F /* Exceptions for "MetronomeApp" folder in "MetronomeApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = D1A3845A2DFE9528008BF38F /* MetronomeApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		D1A3845D2DFE9528008BF38F /* MetronomeApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				D1A384952DFE9623008BF38F /* Exceptions for "MetronomeApp" folder in "MetronomeApp" target */,
			);
			path = "MetronomeApp";
			sourceTree = "<group>";
		};
		D1A3846B2DFE952A008BF38F /* MetronomeAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "MetronomeAppTests";
			sourceTree = "<group>";
		};
		D1A384752DFE952A008BF38F /* MetronomeAppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "MetronomeAppUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		D1A384582DFE9528008BF38F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1A384652DFE952A008BF38F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1A3846F2DFE952A008BF38F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		D1A384522DFE9528008BF38F = {
			isa = PBXGroup;
			children = (
				D1A3845D2DFE9528008BF38F /* MetronomeApp */,
				D1A3846B2DFE952A008BF38F /* MetronomeAppTests */,
				D1A384752DFE952A008BF38F /* MetronomeAppUITests */,
				D1A3845C2DFE9528008BF38F /* Products */,
			);
			sourceTree = "<group>";
		};
		D1A3845C2DFE9528008BF38F /* Products */ = {
			isa = PBXGroup;
			children = (
				D1A3845B2DFE9528008BF38F /* MetronomeApp.app */,
				D1A384682DFE952A008BF38F /* MetronomeAppTests.xctest */,
				D1A384722DFE952A008BF38F /* MetronomeAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		D1A3845A2DFE9528008BF38F /* MetronomeApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D1A3847C2DFE952A008BF38F /* Build configuration list for PBXNativeTarget "MetronomeApp" */;
			buildPhases = (
				D1A384572DFE9528008BF38F /* Sources */,
				D1A384582DFE9528008BF38F /* Frameworks */,
				D1A384592DFE9528008BF38F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				D1A3845D2DFE9528008BF38F /* MetronomeApp */,
			);
			name = "MetronomeApp";
			packageProductDependencies = (
			);
			productName = "MetronomeApp";
			productReference = D1A3845B2DFE9528008BF38F /* MetronomeApp.app */;
			productType = "com.apple.product-type.application";
		};
		D1A384672DFE952A008BF38F /* MetronomeAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D1A3847F2DFE952A008BF38F /* Build configuration list for PBXNativeTarget "MetronomeAppTests" */;
			buildPhases = (
				D1A384642DFE952A008BF38F /* Sources */,
				D1A384652DFE952A008BF38F /* Frameworks */,
				D1A384662DFE952A008BF38F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				D1A3846A2DFE952A008BF38F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				D1A3846B2DFE952A008BF38F /* MetronomeAppTests */,
			);
			name = "MetronomeAppTests";
			packageProductDependencies = (
			);
			productName = "MetronomeAppTests";
			productReference = D1A384682DFE952A008BF38F /* MetronomeAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		D1A384712DFE952A008BF38F /* MetronomeAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D1A384822DFE952A008BF38F /* Build configuration list for PBXNativeTarget "MetronomeAppUITests" */;
			buildPhases = (
				D1A3846E2DFE952A008BF38F /* Sources */,
				D1A3846F2DFE952A008BF38F /* Frameworks */,
				D1A384702DFE952A008BF38F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				D1A384742DFE952A008BF38F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				D1A384752DFE952A008BF38F /* MetronomeAppUITests */,
			);
			name = "MetronomeAppUITests";
			packageProductDependencies = (
			);
			productName = "MetronomeAppUITests";
			productReference = D1A384722DFE952A008BF38F /* MetronomeAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		D1A384532DFE9528008BF38F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					D1A3845A2DFE9528008BF38F = {
						CreatedOnToolsVersion = 16.4;
						LastSwiftMigration = 1640;
					};
					D1A384672DFE952A008BF38F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = D1A3845A2DFE9528008BF38F;
					};
					D1A384712DFE952A008BF38F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = D1A3845A2DFE9528008BF38F;
					};
				};
			};
			buildConfigurationList = D1A384562DFE9528008BF38F /* Build configuration list for PBXProject "MetronomeApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = D1A384522DFE9528008BF38F;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = D1A3845C2DFE9528008BF38F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D1A3845A2DFE9528008BF38F /* MetronomeApp */,
				D1A384672DFE952A008BF38F /* MetronomeAppTests */,
				D1A384712DFE952A008BF38F /* MetronomeAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D1A384592DFE9528008BF38F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1A384662DFE952A008BF38F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1A384702DFE952A008BF38F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		D1A384572DFE9528008BF38F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1A384642DFE952A008BF38F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1A3846E2DFE952A008BF38F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		D1A3846A2DFE952A008BF38F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D1A3845A2DFE9528008BF38F /* MetronomeApp */;
			targetProxy = D1A384692DFE952A008BF38F /* PBXContainerItemProxy */;
		};
		D1A384742DFE952A008BF38F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D1A3845A2DFE9528008BF38F /* MetronomeApp */;
			targetProxy = D1A384732DFE952A008BF38F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		D1A3847A2DFE952A008BF38F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		D1A3847B2DFE952A008BF38F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D1A3847D2DFE952A008BF38F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MQ63M2YZ93;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MetronomeApp/Info.plist";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ljw.MetronomeApp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D1A3847E2DFE952A008BF38F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MQ63M2YZ93;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MetronomeApp/Info.plist";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ljw.MetronomeApp";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		D1A384802DFE952A008BF38F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MQ63M2YZ93;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ljw.MetronomeAppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MetronomeApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MetronomeApp";
			};
			name = Debug;
		};
		D1A384812DFE952A008BF38F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MQ63M2YZ93;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ljw.MetronomeAppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MetronomeApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MetronomeApp";
			};
			name = Release;
		};
		D1A384832DFE952A008BF38F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MQ63M2YZ93;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ljw.MetronomeAppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "MetronomeApp";
			};
			name = Debug;
		};
		D1A384842DFE952A008BF38F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MQ63M2YZ93;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Ljw.MetronomeAppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "MetronomeApp";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		D1A384562DFE9528008BF38F /* Build configuration list for PBXProject "MetronomeApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D1A3847A2DFE952A008BF38F /* Debug */,
				D1A3847B2DFE952A008BF38F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D1A3847C2DFE952A008BF38F /* Build configuration list for PBXNativeTarget "MetronomeApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D1A3847D2DFE952A008BF38F /* Debug */,
				D1A3847E2DFE952A008BF38F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D1A3847F2DFE952A008BF38F /* Build configuration list for PBXNativeTarget "MetronomeAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D1A384802DFE952A008BF38F /* Debug */,
				D1A384812DFE952A008BF38F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D1A384822DFE952A008BF38F /* Build configuration list for PBXNativeTarget "MetronomeAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D1A384832DFE952A008BF38F /* Debug */,
				D1A384842DFE952A008BF38F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = D1A384532DFE9528008BF38F /* Project object */;
}
